package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.Course;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.CourseMapper;
import com.teachingassistant.service.CourseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

/**
 * 课程服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseServiceImpl implements CourseService {
    
    private final CourseMapper courseMapper;
    
    @Override
    public Course findById(Long courseId) {
        if (courseId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
        Course course = courseMapper.findById(courseId);
        if (course == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "课程不存在");
        }
        return course;
    }
    
    @Override
    public List<Course> findBySchoolId(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        return courseMapper.findBySchoolId(schoolId);
    }
    
    @Override
    public List<Course> findByDateRange(Long schoolId, LocalDate startDate, LocalDate endDate, 
                                       Long teacherId, Long classroomId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "日期范围不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "开始日期不能晚于结束日期");
        }
        
        return courseMapper.findByDateRange(schoolId, startDate, endDate, teacherId, classroomId);
    }
    
    @Override
    public PageResult<Course> findWithPagination(Integer page, Integer size, Long schoolId, 
                                                Long teacherId, Long classroomId, String status) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        Integer offset = (page - 1) * size;
        List<Course> courses = courseMapper.findWithPagination(offset, size, schoolId, 
                                                              teacherId, classroomId, status);
        Integer total = courseMapper.countCourses(schoolId, teacherId, classroomId, status);
        
        return PageResult.of(page, size, total.longValue(), courses);
    }
    
    @Override
    @Transactional
    public Course createCourse(Course course) {
        validateCourseForCreate(course);
        
        // 检查时间冲突
        if (hasTimeConflict(course.getTeacherId(), course.getClassroomId(), 
                           course.getCourseDate(), course.getStartTime(), 
                           course.getEndTime(), null)) {
            throw new BusinessException(ResultCode.BUSINESS_ERROR, "该时间段已有课程安排");
        }
        
        // 设置默认状态
        if (!StringUtils.hasText(course.getStatus())) {
            course.setStatus("scheduled");
        }
        
        int result = courseMapper.insert(course);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "课程创建失败");
        }
        
        log.info("课程创建成功: 教师ID={}, 教室ID={}, 日期={}", 
                course.getTeacherId(), course.getClassroomId(), course.getCourseDate());
        return findById(course.getCourseId());
    }
    
    @Override
    @Transactional
    public Course updateCourse(Course course) {
        validateCourseForUpdate(course);
        
        // 检查课程是否存在
        Course existingCourse = findById(course.getCourseId());
        
        // 检查时间冲突（排除当前课程）
        if (hasTimeConflict(course.getTeacherId(), course.getClassroomId(), 
                           course.getCourseDate(), course.getStartTime(), 
                           course.getEndTime(), course.getCourseId())) {
            throw new BusinessException(ResultCode.BUSINESS_ERROR, "该时间段已有课程安排");
        }
        
        int result = courseMapper.update(course);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "课程更新失败");
        }
        
        log.info("课程更新成功: ID={}", course.getCourseId());
        return findById(course.getCourseId());
    }
    
    @Override
    @Transactional
    public void deleteCourse(Long courseId) {
        if (courseId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
        
        // 检查课程是否存在
        Course course = findById(courseId);
        
        int result = courseMapper.deleteById(courseId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "课程删除失败");
        }
        
        log.info("课程删除成功: ID={}", courseId);
    }
    
    @Override
    @Transactional
    public void deleteCourses(List<Long> courseIds) {
        if (courseIds == null || courseIds.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID列表不能为空");
        }
        
        int result = courseMapper.deleteByIds(courseIds);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "批量删除课程失败");
        }
        
        log.info("批量删除课程成功: 数量={}", result);
    }
    
    @Override
    @Transactional
    public void updateCourseStatus(Long courseId, String status) {
        if (courseId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
        if (!StringUtils.hasText(status)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "状态不能为空");
        }
        
        // 检查课程是否存在
        Course course = findById(courseId);
        
        int result = courseMapper.updateStatus(courseId, status);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "更新课程状态失败");
        }
        
        log.info("课程状态更新成功: ID={}, 状态={}", courseId, status);
    }
    
    @Override
    public boolean hasTimeConflict(Long teacherId, Long classroomId, LocalDate courseDate, 
                                  LocalTime startTime, LocalTime endTime, Long excludeCourseId) {
        if (teacherId == null || classroomId == null || courseDate == null || 
            startTime == null || endTime == null) {
            return false;
        }
        
        List<Course> conflictCourses = courseMapper.findConflictCourses(
            teacherId, classroomId, courseDate, startTime, endTime, excludeCourseId);
        
        return !conflictCourses.isEmpty();
    }
    
    @Override
    public Map<LocalDate, List<Map<String, Object>>> getAvailableTimeSlots(Long schoolId, Long teacherId, 
                                                                          Long classroomId, LocalDate startDate, 
                                                                          LocalDate endDate) {
        if (schoolId == null || teacherId == null || classroomId == null || 
            startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "参数不能为空");
        }
        
        Map<LocalDate, List<Map<String, Object>>> availableSlots = new HashMap<>();
        
        // 定义工作时间段（可配置）
        List<Map<String, LocalTime>> workingHours = Arrays.asList(
            Map.of("start", LocalTime.of(8, 0), "end", LocalTime.of(12, 0)),
            Map.of("start", LocalTime.of(14, 0), "end", LocalTime.of(18, 0))
        );
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            List<Map<String, Object>> daySlots = new ArrayList<>();
            
            // 获取当天已有课程
            List<Course> teacherCourses = courseMapper.findByTeacherAndDate(teacherId, currentDate);
            List<Course> classroomCourses = courseMapper.findByClassroomAndDate(classroomId, currentDate);
            
            // 合并已占用时间段
            Set<Map<String, LocalTime>> occupiedSlots = new HashSet<>();
            teacherCourses.forEach(course -> 
                occupiedSlots.add(Map.of("start", course.getStartTime(), "end", course.getEndTime())));
            classroomCourses.forEach(course -> 
                occupiedSlots.add(Map.of("start", course.getStartTime(), "end", course.getEndTime())));
            
            // 计算空闲时段
            for (Map<String, LocalTime> workingHour : workingHours) {
                LocalTime start = workingHour.get("start");
                LocalTime end = workingHour.get("end");
                
                // 以30分钟为单位切分时间段
                LocalTime current = start;
                while (current.isBefore(end)) {
                    LocalTime slotEnd = current.plusMinutes(30);
                    if (slotEnd.isAfter(end)) {
                        slotEnd = end;
                    }
                    
                    // 检查是否与已占用时间段冲突
                    final LocalTime finalCurrent = current;
                    final LocalTime finalSlotEnd = slotEnd;
                    boolean isOccupied = occupiedSlots.stream().anyMatch(occupied -> 
                        !(finalSlotEnd.isBefore(occupied.get("start")) || 
                          finalCurrent.isAfter(occupied.get("end")) ||
                          finalCurrent.equals(occupied.get("end"))));
                    
                    if (!isOccupied) {
                        Map<String, Object> slot = new HashMap<>();
                        slot.put("startTime", current);
                        slot.put("endTime", slotEnd);
                        slot.put("available", true);
                        daySlots.add(slot);
                    }
                    
                    current = slotEnd;
                }
            }
            
            availableSlots.put(currentDate, daySlots);
            currentDate = currentDate.plusDays(1);
        }
        
        return availableSlots;
    }
    
    @Override
    public boolean hasPermissionToAccess(Long courseId, Long userSchoolId, String userRole) {
        if (courseId == null || userSchoolId == null || !StringUtils.hasText(userRole)) {
            return false;
        }
        
        Course course = courseMapper.findById(courseId);
        if (course == null) {
            return false;
        }
        
        // 超级管理员有所有权限
        if ("super_admin".equals(userRole)) {
            return true;
        }
        
        // 校长和老师只能访问本校的课程
        return course.getSchoolId().equals(userSchoolId);
    }
    
    @Override
    public Map<String, Object> getCourseStatistics(Long schoolId, LocalDate startDate, LocalDate endDate) {
        if (schoolId == null || startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "参数不能为空");
        }
        
        List<Course> courses = courseMapper.findByDateRange(schoolId, startDate, endDate, null, null);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCourses", courses.size());
        statistics.put("scheduledCourses", courses.stream().mapToInt(c -> "scheduled".equals(c.getStatus()) ? 1 : 0).sum());
        statistics.put("completedCourses", courses.stream().mapToInt(c -> "completed".equals(c.getStatus()) ? 1 : 0).sum());
        statistics.put("cancelledCourses", courses.stream().mapToInt(c -> "cancelled".equals(c.getStatus()) ? 1 : 0).sum());
        
        return statistics;
    }
    
    private void validateCourseForCreate(Course course) {
        if (course == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程信息不能为空");
        }
        if (course.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (course.getTeacherId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教师ID不能为空");
        }
        if (course.getStudentId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生ID不能为空");
        }
        if (course.getClassroomId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室ID不能为空");
        }
        if (course.getCourseDate() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "上课日期不能为空");
        }
        if (course.getStartTime() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "开始时间不能为空");
        }
        if (course.getEndTime() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "结束时间不能为空");
        }
        if (course.getStartTime().isAfter(course.getEndTime()) || 
            course.getStartTime().equals(course.getEndTime())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "开始时间必须早于结束时间");
        }
        if (course.getPrice() == null || course.getPrice().compareTo(java.math.BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程价格不能为空且不能为负数");
        }
    }
    
    private void validateCourseForUpdate(Course course) {
        validateCourseForCreate(course);
        if (course.getCourseId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
    }
}
