<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.StudentMapper">

    <!-- 结果映射 -->
    <resultMap id="StudentResultMap" type="com.teachingassistant.entity.Student">
        <id property="studentId" column="student_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="name" column="name"/>
        <result property="classId" column="class_id"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="unpaidAmount" column="unpaid_amount"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <!-- 关联查询 -->
        <association property="schoolClass" javaType="com.teachingassistant.entity.SchoolClass">
            <id property="classId" column="class_class_id"/>
            <result property="name" column="class_name"/>
            <result property="gradeLevel" column="class_grade_level"/>
        </association>
        <association property="school" javaType="com.teachingassistant.entity.School">
            <id property="schoolId" column="school_school_id"/>
            <result property="name" column="school_name"/>
            <result property="address" column="school_address"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        s.student_id, s.school_id, s.name, s.class_id,
        s.contact_phone, s.unpaid_amount, s.created_at, s.updated_at
    </sql>

    <!-- 关联查询字段 -->
    <sql id="JoinColumns">
        <include refid="BaseColumns"/>,
        c.class_id as class_class_id, c.name as class_name, c.grade_level as class_grade_level,
        sc.school_id as school_school_id, sc.name as school_name, sc.address as school_address
    </sql>

    <!-- 基础表连接 -->
    <sql id="BaseJoins">
        LEFT JOIN classes c ON s.class_id = c.class_id
        LEFT JOIN schools sc ON s.school_id = sc.school_id
    </sql>

    <!-- 查询条件 -->
    <sql id="WhereConditions">
        <where>
            <if test="schoolId != null">
                AND s.school_id = #{schoolId}
            </if>
            <if test="classId != null">
                AND s.class_id = #{classId}
            </if>
            <if test="name != null and name != ''">
                AND s.name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </sql>

    <!-- 根据学生ID查询学生 -->
    <select id="findById" resultMap="StudentResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM students s
        <include refid="BaseJoins"/>
        WHERE s.student_id = #{studentId}
    </select>

    <!-- 根据学校ID查询学生列表 -->
    <select id="findBySchoolId" resultMap="StudentResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM students s
        <include refid="BaseJoins"/>
        WHERE s.school_id = #{schoolId}
        ORDER BY s.name ASC
    </select>

    <!-- 根据班级ID查询学生列表 -->
    <select id="findByClassId" resultMap="StudentResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM students s
        <include refid="BaseJoins"/>
        WHERE s.class_id = #{classId}
        ORDER BY s.name ASC
    </select>

    <!-- 分页查询学生列表 -->
    <select id="findWithPagination" resultMap="StudentResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM students s
        <include refid="BaseJoins"/>
        <include refid="WhereConditions"/>
        ORDER BY s.name ASC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计学生总数 -->
    <select id="countStudents" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM students s
        <include refid="WhereConditions"/>
    </select>

    <!-- 插入学生 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="studentId">
        INSERT INTO students (
            school_id, name, class_id, contact_phone, unpaid_amount
        ) VALUES (
            #{schoolId}, #{name}, #{classId}, #{contactPhone}, #{unpaidAmount}
        )
    </insert>

    <!-- 更新学生信息 -->
    <update id="update">
        UPDATE students SET
            name = #{name},
            class_id = #{classId},
            contact_phone = #{contactPhone},
            unpaid_amount = #{unpaidAmount},
            updated_at = CURRENT_TIMESTAMP
        WHERE student_id = #{studentId}
    </update>

    <!-- 删除学生 -->
    <delete id="deleteById">
        DELETE FROM students WHERE student_id = #{studentId}
    </delete>

    <!-- 检查学生姓名是否在同一班级内存在 -->
    <select id="existsByNameAndClassId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM students
        WHERE name = #{name} AND class_id = #{classId}
    </select>

    <!-- 检查学生姓名是否在同一班级内存在（排除指定ID） -->
    <select id="existsByNameAndClassIdExcludeId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM students
        WHERE name = #{name} AND class_id = #{classId} AND student_id != #{studentId}
    </select>

    <!-- 根据多个学生ID查询学生列表 -->
    <select id="findByIds" resultMap="StudentResultMap">
        SELECT <include refid="JoinColumns"/>
        FROM students s
        <include refid="BaseJoins"/>
        WHERE s.student_id IN
        <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
            #{studentId}
        </foreach>
        ORDER BY s.name ASC
    </select>

</mapper>
