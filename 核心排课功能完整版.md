核心目标​
开发类Microsoft Teams的交互式排课系统，包含双视图模式（查看排课/新增排课），支持按教室、教师智能筛选，并实现空闲时段自动化计算。
一、排课表基础框架​
​时空坐标系​
横轴：周一到周日（动态日期范围）
纵轴：00:00-24:00（刻度精确到30分钟）
当前时间线：红色横条动态标记此刻时间点
​数据加载逻辑​
初始默认展示：系统查询到的「第一个教室」未来7天排课
翻页机制：支持查看过去7天及未来30天数据
实时同步：新增排课后即时刷新视图

二、智能筛选系统（左侧侧边栏）​​
功能	交互规则
​教室筛选器​	点击教室名称 → 右侧展示该教室本周所有排课
​教师筛选器​	点击教师姓名 → 右侧展示该教师本周授课计划
​双条件筛选​	同时选中教室+教师 → 仅展示该教师在此教室的排课
​侧边栏特性​	支持折叠/展开，默认展开状态
​初始值规则​：进入页面时自动选中查询到的第一个教室/教师
三、双视图切换（核心创新模块）​​
​视图切换按钮​
位置：侧边栏顶部
状态：
查看模式（默认）：显示现有排课
新增模式：激活空闲时段计算与排课交互
​新增排课视图工作流​
​条件继承​
自动继承当前选中的教室/教师（未选择时取首条数据）
​空闲时段计算
界面表现：空闲区域用半透明绿色高亮覆盖

四.新增排课功能逻辑实现

在左侧侧边栏的左上角新增一个切换视图按钮，该按钮可以切换右侧排课表的视图。
默认是查看排课视图，当我点击按钮切换成新增排课视图以后，会根据当前选中的教室和老师(查看视图时选中的教室和老师，若没有则默认取列表查询到的第一条数据)自动计算出允许排课的时间段(通过计算教室和老师空闲时间的重叠部分)，
并在右侧排课表里用特殊的颜色进行空闲区域标记，以便用户看到能够直观地看到允许排课的时间段

以下是用户进行新增排课的具体流程：

1.首先确定要新增排课的教室和老师

2.确定上课时间，把鼠标移动到右侧排课表计算出来的允许排课(特殊颜色标记)的区域，点击左键会弹出一个框，该弹框会根据点击的区域所在列自动显示出星期数，如星期一，星期二等。
并且自动显示出空余时间段的开始时间和结束时间。然后可以让用户手动修改开始时间和结束时间，但是要限制不能超出允许排课的时间段。

3.确定上课人数及统计学生姓名。继续在第二点的弹窗新增选项框，选择好时间后，该弹框还有其它填写项。
通过列表查询接口展示学生信息。可以添加多个学生，选定好学生以后，会自动计算出总人数并展示出来

4.确定该节课的价格，继续在第二点的弹窗新增输入框。手动输入该节课的总价格。

至此，表单填写完成。点击确认，即可在右侧排课表里看到自己新增的排课计划。